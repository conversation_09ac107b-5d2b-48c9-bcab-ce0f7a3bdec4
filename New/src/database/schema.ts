/**
 * Database schema definitions for DermaCare bi-directional sync service
 *
 * This module defines the PostgreSQL database schema using Drizzle ORM for the
 * DermaCare data synchronization service. The schema supports bi-directional
 * data sync between CliniCore (CC) and AutoPatient (AP) platforms with proper
 * relationship management, data integrity, and performance optimization.
 *
 * **Schema Design Principles:**
 * - Maintains data from both platforms for comparison and conflict resolution
 * - Uses UUID primary keys for distributed system compatibility
 * - Stores platform-specific timestamps for sync coordination
 * - Implements JSONB columns for flexible data storage
 * - Includes proper indexing for query performance
 *
 * **Key Features:**
 * - Patient-appointment relationship management
 * - Custom field caching for both platforms
 * - Comprehensive error logging with deduplication
 * - Audit trail with creation and update timestamps
 * - Platform-specific data preservation
 *
 * **Performance Optimizations:**
 * - Unique indexes on platform-specific IDs
 * - JSONB columns for efficient JSON operations
 * - Proper foreign key relationships
 * - Optimized query patterns for sync operations
 *
 * @example
 * ```typescript
 * // Query patient with appointments
 * const patientWithAppointments = await db
 *   .select()
 *   .from(patient)
 *   .leftJoin(appointment, eq(appointment.patientId, patient.id))
 *   .where(eq(patient.ccId, 123));
 *
 * // Insert new patient record
 * const newPatient = await db.insert(patient).values({
 *   apId: "ap_contact_123",
 *   ccId: 456,
 *   email: "<EMAIL>",
 *   apData: apContactData,
 *   ccData: ccPatientData
 * });
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import type {
	APGetCustomFieldType,
	GetAPAppointmentType,
	GetAPContactType,
	GetCCAppointmentType,
	GetCCCustomfieldsType,
	GetCCPatientType,
} from "@type";
import { relations } from "drizzle-orm";
import {
	customType,
	integer,
	jsonb,
	pgTable,
	text,
	timestamp,
	varchar,
} from "drizzle-orm/pg-core";
import type { APContactCreationWebhookPayload } from "@/processors/apWebhook";
import type { CCWebhookPayload } from "@/processors/ccWebhook";

/**
 * Custom JSONB column type with proper TypeScript typing
 *
 * Creates a strongly-typed JSONB column that automatically handles JSON
 * serialization/deserialization while maintaining type safety throughout
 * the application. This is essential for storing complex platform-specific
 * data structures.
 *
 * @template TData - TypeScript type for the JSON data structure
 * @param name - Column name in the database
 * @returns Drizzle column definition with proper typing
 *
 * @example
 * ```typescript
* // Define a JSONB column for AP contact data
 * const apData = customJsonb<GetAPContactType>("ap_data");
 *
 * // Usage in table definition
 * export const patient = pgTable("patients", {
 *   apData: customJsonb<GetAPContactType>("ap_data"),
 *   ccData: customJsonb<GetCCPatientType>("cc_data")
 * });
 *
```
*/
const customJsonb = <TData>(name: string) =>
	customType<{ data: TData; driverData: string }>({
		dataType() {
			return "jsonb";
		},
		toDriver(value: TData): string {
			return JSON.stringify(value);
		},
	})(name);

/**
 * Common columns shared across all tables
 *
 * Provides consistent structure for all database tables including:
 * - UUID primary key for distributed system compatibility
 * - Creation timestamp for audit trails
 * - Update timestamp with automatic updates
 *
 * **Column Details:**
 * - `id`: UUID primary key, automatically generated
 * - `createdAt`: Timestamp when record was created, defaults to now
 * - `updatedAt`: Timestamp when record was last updated, auto-updates
 *
 * @example
 *
```typescript
* // Use in table definition
 * export const myTable = pgTable("my_table", {
 *   ...commonColumns,
 *   customField: varchar("custom_field", { length: 255 })
 * });
 *
```
*/
const commonColumns = {
	/** UUID primary key, automatically generated using crypto.randomUUID() */
	id: varchar("id", { length: 255 })
		.primaryKey()
		.$defaultFn(() => crypto.randomUUID()),
	/** Record creation timestamp, defaults to current time */
	createdAt: timestamp("created_at").notNull().defaultNow(),
	/** Record update timestamp, automatically updated on changes */
	updatedAt: timestamp("updated_at")
		.notNull()
		.defaultNow()
		.$onUpdate(() => new Date()),
};

/**
 * Patient table for bi-directional sync between CC and AP platforms
 *
 * Central table that maintains patient/contact data from both CliniCore (CC) and
 * AutoPatient (AP) platforms. This table serves as the primary synchronization
 * point and enables conflict resolution by storing data from both systems.
 *
 * **Key Features:**
 * - Stores both AP contact ID and CC patient ID for cross-platform linking
 * - Maintains platform-specific update timestamps for sync coordination
 * - Preserves complete data from both platforms in JSONB columns
 * - Includes email and phone for quick lookup and matching
 * - Supports one-to-many relationship with appointments
 *
 * **Sync Logic:**
 * - Records are created when data arrives from either platform
 * - Platform-specific IDs are populated when sync occurs
 * - Update timestamps track when each platform last modified the record
 * - JSONB data preserves complete platform-specific information
 *
 * **Indexing:**
 * - Unique indexes on apId and ccId for fast platform-specific lookups
 * - Email and phone columns for duplicate detection and matching
 * - Primary key (UUID) for efficient joins with appointments
 *
 * @example
 *
```typescript
* // Find patient by CC ID
 * const patient = await db.select().from(patient).where(eq(patient.ccId, 123));
 *
 * // Find patient by AP ID
 * const patient = await db.select().from(patient).where(eq(patient.apId, "ap_123"));
 *
```
 */
export const patient = pgTable("patients", {
	...commonColumns,
	/** AutoPatient contact ID, unique identifier from AP platform */
	apId: varchar("ap_id", { length: 255 }),
	/** CliniCore patient ID, unique identifier from CC platform */
	ccId: integer("cc_id"),
	/** Patient email address for matching and communication */
	email: varchar("email", { length: 255 }),
	/** Patient phone number for matching and communication */
	phone: varchar("phone", { length: 255 }),
	/** Timestamp when patient was last updated in AP platform */
	apUpdatedAt: timestamp("ap_updated_at"),
	/** Timestamp when patient was last updated in CC platform */
	ccUpdatedAt: timestamp("cc_updated_at"),
	/** Complete AP contact data in JSONB format for preservation */
	apData: customJsonb<GetAPContactType>("ap_data"),
	/** Complete CC patient data in JSONB format for preservation */
	ccData: customJsonb<GetCCPatientType>("cc_data"),
});

export const appointment = pgTable("appointments", {
	...commonColumns,
	apId: varchar("ap_id", { length: 255 }).unique(),
	ccId: integer("cc_id").unique(),
	patientId: varchar("patient_id", { length: 255 }).references(
		() => patient.id,
	),
	apUpdatedAt: timestamp("ap_updated_at"),
	ccUpdatedAt: timestamp("cc_updated_at"),
	apData: customJsonb<GetAPAppointmentType>("ap_data"),
	ccData: customJsonb<GetCCAppointmentType>("cc_data"),
	apNoteID: text("ap_note_id"),
});

export const customFields = pgTable("custom_fields", {
	...commonColumns,
	apId: varchar("ap_id", { length: 255 }).unique(),
	ccId: integer("cc_id").unique(),
	name: varchar("name", { length: 255 }),
	label: varchar("label", { length: 255 }),
	type: varchar("type", { length: 255 }),
	apConfig: customJsonb<APGetCustomFieldType>("ap_config"),
	ccConfig: customJsonb<GetCCCustomfieldsType>("cc_config"),
	/** Mapping type: custom_to_custom, custom_to_standard, standard_to_custom */
	mappingType: varchar("mapping_type", { length: 50 }).default(
		"custom_to_custom",
	),
	/** AP standard field name if this maps to an AP standard field */
	apStandardField: varchar("ap_standard_field", { length: 255 }),
	/** CC standard field name if this maps to a CC standard field */
	ccStandardField: varchar("cc_standard_field", { length: 255 }),
});

export const errorLogs = pgTable("error_logs", {
	...commonColumns,
	requestId: varchar("request_id", { length: 255 }),
	message: text("message").notNull(),
	stack: text("stack"),
	type: varchar("type", { length: 255 }).notNull(),
	data: jsonb("data"),
});

export const webhooks = pgTable("webhooks", {
	...commonColumns,
	requestId: varchar("request_id", { length: 255 }).unique(),
	payload: jsonb("payload").notNull(),
	source: varchar("source", { length: 64, enum: ["cc", "ap"] }).notNull(),
});

export const patientAppointmentsRelation = relations(patient, ({ many }) => ({
	appointments: many(appointment),
}));

export const appointmentPatientRelation = relations(appointment, ({ one }) => ({
	patient: one(patient, {
		fields: [appointment.patientId],
		references: [patient.id],
	}),
}));

export const webhookQueue = pgTable("webhook_queue", {
	...commonColumns,
	source: text("source").notNull(), // "ap" or "cc"
	sourceId: text("source_id").notNull(), // AP ID or CC ID
	type: varchar("type", { length: 255, enum: ["patient", "appointment"] })
		.notNull()
		.default("patient"), // "patient_created", "patient_updated", etc.
	payload: customJsonb<APContactCreationWebhookPayload | CCWebhookPayload>(
		"payload",
	).notNull(), // JSON data
	status: varchar("status", {
		length: 255,
		enum: ["pending", "processing", "completed", "failed"],
	})
		.notNull()
		.default("pending"),
	retryCount: integer("retry_count").notNull().default(0),
	lastRetryAttemptAt: timestamp("last_retry_attempt_at"),
	lastRetryReason: text("last_retry_reason"),
	maxRetries: integer("max_retries").notNull().default(3),
	processingStartedAt: timestamp("processing_started_at"),
	processingCompletedAt: timestamp("processing_completed_at"),
	errorMessage: text("error_message"),
	errorDetails: customJsonb<Record<string, unknown>>("error_details"),
});

export const queueLogs = pgTable("queue_logs", {
	...commonColumns,
	// Original webhook queue fields
	originalId: varchar("original_id", { length: 255 }).notNull(), // Original webhook queue ID
	source: text("source").notNull(), // "ap" or "cc"
	sourceId: text("source_id").notNull(), // AP ID or CC ID
	type: varchar("type", { length: 255, enum: ["patient", "appointment"] })
		.notNull()
		.default("patient"), // "patient_created", "patient_updated", etc.
	payload: customJsonb<APContactCreationWebhookPayload | CCWebhookPayload>(
		"payload",
	).notNull(), // JSON data
	status: varchar("status", {
		length: 255,
		enum: ["pending", "processing", "completed", "failed"],
	})
		.notNull()
		.default("pending"),
	retryCount: integer("retry_count").notNull().default(0),
	lastRetryAttemptAt: timestamp("last_retry_attempt_at"),
	lastRetryReason: text("last_retry_reason"),
	maxRetries: integer("max_retries").notNull().default(3),
	processingStartedAt: timestamp("processing_started_at"),
	processingCompletedAt: timestamp("processing_completed_at"),
	errorMessage: text("error_message"),
	errorDetails: customJsonb<Record<string, unknown>>("error_details"),
	// Original timestamps from webhook queue
	originalCreatedAt: timestamp("original_created_at").notNull(),
	originalUpdatedAt: timestamp("original_updated_at").notNull(),
	// Audit fields
	deletedAt: timestamp("deleted_at").notNull().defaultNow(),
	deletionReason: varchar("deletion_reason", { length: 255 }).notNull(), // "completed", "duplicate_prevention", "failed", "stuck"
	deletionContext: customJsonb<Record<string, unknown>>("deletion_context"), // Additional context about deletion
});

export type WebhookQueue = typeof webhookQueue.$inferSelect;

export const dbSchema = {
	patient,
	appointment,
	customFields,
	errorLogs,
	webhooks,
	webhookQueue,
	queueLogs,
};
