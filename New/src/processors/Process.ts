/**
 * Webhook Queue Process Management
 *
 * Provides a comprehensive class for managing webhook queue operations with
 * state management, database integration, and Hono context support. Handles
 * webhook lifecycle from creation to completion with proper error handling
 * and thread-safety considerations.
 *
 * @fileoverview Webhook queue process management class
 * @version 1.0.0
 * @since 2024-08-04
 */

import { dbSchema, getDb } from "@database";
import type { WebhookQueue } from "@database/schema";
import { and, eq } from "drizzle-orm";
import { getContext } from "hono/context-storage";
import { getRequestId } from "@/utils/getRequestId";
import { logDebug, logError, logInfo } from "@/utils/logger";


/**
 * Queue processing state interface
 */
interface QueueProcessingState {
	isProcessing: boolean;
	processingStartedAt?: Date;
	activeWebhookId?: string;
	activeWebhookSource?: string;
	activeWebhookSourceId?: string;
}

/**
 * Webhook status check result interface
 */
interface WebhookStatusResult {
	exists: boolean;
	status?: "pending" | "processing" | "completed" | "failed";
	webhookId?: string;
	processingStartedAt?: Date;
	processingCompletedAt?: Date;
	errorMessage?: string;
}

/**
 * Process class for webhook queue management
 *
 * Manages webhook queue operations with state binding and lifecycle management.
 * After calling `add()` or `inProcess()`, the instance is bound to a specific
 * webhook record and subsequent operations work on that record.
 *
 * @example
 * ```typescript
 * // Add new webhook to queue
 * const process = new Process();
 * await process.add(webhookPayload);
 * await process.processing();
 * await process.complete();
 *
 * // Work with existing webhook
 * const process = new Process();
 * await process.inProcess("contact_123", "patient");
 * await process.processing();
 * // ... do work ...
 * await process.complete();
 * ```
 */
export class Process {
	private webhookRecord?: WebhookQueue;
	private readonly db = getDb();

	/**
	 * Add a new webhook to the queue
	 *
	 * Creates a new webhook queue record and binds this instance to it.
	 * The webhook starts in "pending" status.
	 *
	 * @param payload - Webhook payload (AP or CC)
	 * @throws {Error} When payload is invalid or database operation fails
	 */
	async add(
		payload: APContactCreationWebhookPayload | CCWebhookPayload,
	): Promise<void> {
		const requestId = getRequestId();

		try {
			const sourceInfo = extractSourceInfo(payload);

			const webhookId = crypto.randomUUID();
			const now = new Date();

			const webhookData = {
				id: webhookId,
				source: sourceInfo.source,
				sourceId: sourceInfo.sourceId,
				type: sourceInfo.type as "patient" | "appointment",
				payload: payload,
				status: "pending" as const,
				retryCount: 0,
				maxRetries: 3,
				createdAt: now,
				updatedAt: now,
			};

			// Add webhook to queue with duplicate prevention
			const createdWebhook = await addWebhookToQueue(this.db, webhookData);

			// Bind this instance to the created webhook record
			this.webhookRecord = createdWebhook;

			logInfo(
				`Webhook added to queue: ${createdWebhook.id} (${sourceInfo.source}:${sourceInfo.sourceId})`,
			);
		} catch (error) {
			// Check if this is a duplicate prevention error
			if (error instanceof Error && error.message.includes("Duplicate prevention")) {
				logInfo(
					`Webhook skipped due to duplicate prevention: ${error.message}`,
					{
						type: "webhook_queue_duplicate_prevention",
						data: { requestId, error: error.message },
					},
				);
				// Don't throw error for duplicate prevention - this is expected behavior
				return;
			}

			logError(`Failed to add webhook to queue: ${error}`, {
				type: "webhook_queue_add",
				data: { requestId, error },
			});
			throw new Error(`Failed to add webhook to queue: ${error}`);
		}
	}

	/**
	 * Bind to an existing webhook in the queue
	 *
	 * Finds and binds this instance to an existing webhook record by sourceId and type.
	 * The webhook must exist and be in a valid state for processing.
	 *
	 * @param sourceId - Source ID (AP contact/appointment ID or CC entity ID)
	 * @param type - Webhook type (default: "patient")
	 * @throws {Error} When webhook not found or database operation fails
	 */
	async inProcess(
		sourceId: string,
		type: "patient" | "appointment" = "patient",
	): Promise<void> {
		const requestId = getRequestId();

		try {
			const [record] = await this.db
				.select()
				.from(dbSchema.webhookQueue)
				.where(
					and(
						eq(dbSchema.webhookQueue.sourceId, sourceId),
						eq(dbSchema.webhookQueue.type, type),
					),
				)
				.limit(1);

			if (!record) {
				throw new Error(
					`Webhook not found for sourceId: ${sourceId}, type: ${type}`,
				);
			}

			this.webhookRecord = record;

			logDebug(`Process bound to webhook: ${record.id} (${sourceId}:${type})`);
		} catch (error) {
			logError(`Failed to bind to webhook: ${error}`, {
				type: "webhook_queue_bind",
				data: { requestId, sourceId, type, error },
			});
			throw new Error(`Failed to bind to webhook: ${error}`);
		}
	}

	/**
	 * Remove the webhook from the queue
	 *
	 * Permanently deletes the bound webhook record from the database.
	 * This operation cannot be undone.
	 *
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async remove(): Promise<void> {
		if (!this.webhookRecord) {
			throw new Error(
				"Process not bound to a webhook. Call add() or inProcess() first.",
			);
		}
		const requestId = getRequestId();

		try {
			await this.db
				.delete(dbSchema.webhookQueue)
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord.id));

			logInfo(`Webhook removed from queue: ${this.webhookRecord.id}`);
			this.webhookRecord = undefined;
		} catch (error) {
			logError(`Failed to remove webhook: ${error}`, {
				type: "webhook_queue_remove",
				data: { requestId, webhookId: this.webhookRecord?.id, error },
			});
			throw new Error(`Failed to remove webhook: ${error}`);
		}
	}

	/**
	 * Mark webhook for retry
	 *
	 * Keeps the webhook in pending status, increments retry count,
	 * and optionally saves a retry reason.
	 *
	 * @param message - Optional retry reason message
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async tryAgain(message?: string): Promise<void> {
		if (!this.webhookRecord) {
			throw new Error(
				"Process not bound to a webhook. Call add() or inProcess() first.",
			);
		}
		const requestId = getRequestId();

		try {
			if (!this.webhookRecord) {
				throw new Error("No webhook record available for retry");
			}

			const now = new Date();

			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "pending",
					retryCount: this.webhookRecord.retryCount + 1,
					lastRetryAttemptAt: now,
					lastRetryReason: message || null,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord.id));

			// Update local record
			this.webhookRecord = {
				...this.webhookRecord,
				status: "pending",
				retryCount: this.webhookRecord.retryCount + 1,
				lastRetryAttemptAt: now,
				lastRetryReason: message || null,
				updatedAt: now,
			};

			logInfo(
				`Webhook marked for retry: ${this.webhookRecord.id} (attempt ${this.webhookRecord.retryCount})`,
			);
		} catch (error) {
			logError(`Failed to mark webhook for retry: ${error}`, {
				type: "webhook_queue_retry",
				data: { requestId, webhookId: this.webhookRecord?.id, error },
			});
			throw new Error(`Failed to mark webhook for retry: ${error}`);
		}
	}

	/**
	 * Mark webhook as failed
	 *
	 * Sets the webhook status to "failed" with error message and optional details.
	 * This is a terminal state for the webhook.
	 *
	 * @param message - Error message describing the failure
	 * @param details - Optional error details object or Error instance
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async failed(
		message: string,
		details?: Record<string, unknown> | Error,
	): Promise<void> {
		const requestId = getRequestId();

		try {
			if (!this.webhookRecord) {
				throw new Error("No webhook record available to mark as failed");
			}

			const now = new Date();

			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "failed" as const,
					errorMessage: message,
					errorDetails:
						(details instanceof Error
							? {
									name: details.name,
									message: details.message,
									stack: details.stack,
								}
							: details) || null,
					processingCompletedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord.id));

			// Update local record
			this.webhookRecord = {
				...this.webhookRecord,
				status: "failed",
				errorMessage: message,
				errorDetails:
					(details instanceof Error
						? {
								name: details.name,
								message: details.message,
								stack: details.stack,
							}
						: details) || null,
				processingCompletedAt: now,
				updatedAt: now,
			};

			logError(
				`Webhook marked as failed: ${this.webhookRecord.id} - ${message}`,
				{
					type: "webhook_queue_failed",
					data: { requestId, webhookId: this.webhookRecord.id, details },
				},
			);
		} catch (error) {
			logError(`Failed to mark webhook as failed: ${error}`, {
				type: "webhook_queue_fail_update",
				data: { requestId, webhookId: this.webhookRecord?.id, error },
			});
			throw new Error(`Failed to mark webhook as failed: ${error}`);
		}
	}

	/**
	 * Mark webhook as completed
	 *
	 * Sets the webhook status to "completed" indicating successful processing.
	 * This is a terminal state for the webhook.
	 *
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async complete(): Promise<void> {
		const requestId = getRequestId();

		try {
			if (!this.webhookRecord) {
				throw new Error("No webhook record available to mark as completed");
			}

			const now = new Date();

			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "completed",
					processingCompletedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord.id));

			// Update local record
			this.webhookRecord = {
				...this.webhookRecord,
				status: "completed",
				processingCompletedAt: now,
				updatedAt: now,
			};

			logInfo(`Webhook completed: ${this.webhookRecord.id}`);
		} catch (error) {
			logError(`Failed to mark webhook as completed: ${error}`, {
				type: "webhook_queue_complete",
				data: { requestId, webhookId: this.webhookRecord?.id, error },
			});
			throw new Error(`Failed to mark webhook as completed: ${error}`);
		}
	}

	/**
	 * Mark webhook as currently being processed
	 *
	 * Sets the webhook status to "processing" and records the processing start time.
	 * This indicates active processing is in progress.
	 *
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async processing(): Promise<void> {
		const requestId = getRequestId();

		try {
			if (!this.webhookRecord) {
				throw new Error("No webhook record available to mark as processing");
			}

			const now = new Date();

			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "processing",
					processingStartedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord.id));

			// Update local record
			this.webhookRecord = {
				...this.webhookRecord,
				status: "processing",
				processingStartedAt: now,
				updatedAt: now,
			};

			logDebug(`Webhook processing started: ${this.webhookRecord.id}`);
		} catch (error) {
			logError(`Failed to mark webhook as processing: ${error}`, {
				type: "webhook_queue_processing",
				data: { requestId, webhookId: this.webhookRecord?.id, error },
			});
			throw new Error(`Failed to mark webhook as processing: ${error}`);
		}
	}

	/**
	 * Get the current webhook record
	 *
	 * Returns the bound webhook record for inspection.
	 *
	 * @returns The bound webhook record or undefined if not bound
	 */
	getRecord(): WebhookQueue | undefined {
		return this.webhookRecord;
	}

	/**
	 * Check if the instance is bound to a webhook
	 *
	 * @returns True if bound to a webhook record
	 */
	isBound(): boolean {
		return this.webhookRecord !== undefined;
	}

	/**
	 * Check if any webhook processing is currently active
	 *
	 * Queries the database to determine if there are any webhooks currently
	 * in "processing" status, indicating that webhook processing is active.
	 * This is used to prevent concurrent processing chains.
	 *
	 * @returns Promise resolving to processing state information
	 */
	static async isQueueProcessing(): Promise<QueueProcessingState> {
		try {
			const db = getDb();

			// Check for any webhooks currently in "processing" status
			const processingWebhooks = await db
				.select({
					id: dbSchema.webhookQueue.id,
					source: dbSchema.webhookQueue.source,
					sourceId: dbSchema.webhookQueue.sourceId,
					processingStartedAt: dbSchema.webhookQueue.processingStartedAt,
				})
				.from(dbSchema.webhookQueue)
				.where(eq(dbSchema.webhookQueue.status, "processing"))
				.limit(1);

			if (processingWebhooks.length === 0) {
				return {
					isProcessing: false,
				};
			}

			const activeWebhook = processingWebhooks[0];
			if (activeWebhook.processingStartedAt) {
				const now = new Date();
				const processingDuration =
					now.getTime() - activeWebhook.processingStartedAt.getTime();
				if (processingDuration >= 25000) {
					logError(
						`Webhook processing appears to be stuck. Last started at ${activeWebhook.processingStartedAt} ` +
							`(${processingDuration}ms ago). Marking as failed.`,
					);
					await db
						.update(dbSchema.webhookQueue)
						.set({
							status: "failed",
							errorMessage: "Processing appears to be stuck",
							processingCompletedAt: now,
							updatedAt: now,
						})
						.where(eq(dbSchema.webhookQueue.id, activeWebhook.id));
					return {
						isProcessing: false,
					};
				}
			}
			return {
				isProcessing: true,
				processingStartedAt: activeWebhook.processingStartedAt || undefined,
				activeWebhookId: activeWebhook.id,
				activeWebhookSource: activeWebhook.source,
				activeWebhookSourceId: activeWebhook.sourceId,
			};
		} catch (error) {
			logError("Failed to check queue processing state", error);
			// Return false on error to allow processing (fail-safe)
			return {
				isProcessing: false,
			};
		}
	}

	/**
	 * Check webhook status by source ID
	 *
	 * Queries the database to find webhook status for a specific source ID.
	 * This allows checking if a particular entity (patient/contact) has
	 * webhooks in the queue and their current processing status.
	 *
	 * @param sourceId - Source ID to check (AP contact ID or CC entity ID)
	 * @param type - Webhook type (default: "patient")
	 * @returns Promise resolving to webhook status information
	 */
	static async checkWebhookStatus(
		sourceId: string,
		type: "patient" | "appointment" = "patient",
	): Promise<WebhookStatusResult> {
		try {
			const db = getDb();

			// Find the most recent webhook for this sourceId and type
			const webhooks = await db
				.select({
					id: dbSchema.webhookQueue.id,
					status: dbSchema.webhookQueue.status,
					processingStartedAt: dbSchema.webhookQueue.processingStartedAt,
					processingCompletedAt: dbSchema.webhookQueue.processingCompletedAt,
					errorMessage: dbSchema.webhookQueue.errorMessage,
				})
				.from(dbSchema.webhookQueue)
				.where(
					and(
						eq(dbSchema.webhookQueue.sourceId, sourceId),
						eq(dbSchema.webhookQueue.type, type),
					),
				)
				.orderBy(dbSchema.webhookQueue.createdAt)
				.limit(1);

			if (webhooks.length === 0) {
				return {
					exists: false,
				};
			}

			const webhook = webhooks[0];
			
			return {
				exists: true,
				status: webhook.status as
					| "pending"
					| "processing"
					| "completed"
					| "failed",
				webhookId: webhook.id,
				processingStartedAt: webhook.processingStartedAt || undefined,
				processingCompletedAt: webhook.processingCompletedAt || undefined,
				errorMessage: webhook.errorMessage || undefined,
			};
		} catch (error) {
			logError(
				`Failed to check webhook status for sourceId: ${sourceId}`,
				error,
			);
			return {
				exists: false,
			};
		}
	}

	/**
	 * Get queue statistics
	 *
	 * Provides comprehensive statistics about the webhook queue including
	 * counts by status, processing information, and queue health metrics.
	 *
	 * @returns Promise resolving to queue statistics
	 */
	static async getQueueStats(): Promise<{
		pending: number;
		processing: number;
		completed: number;
		failed: number;
		total: number;
		isProcessing: boolean;
		activeWebhook?: {
			id: string;
			source: string;
			sourceId: string;
			startedAt?: Date;
		};
	}> {
		try {
			const db = getDb();

			// Get counts by status
			const statusCounts = await db
				.select({
					status: dbSchema.webhookQueue.status,
					count: dbSchema.webhookQueue.id,
				})
				.from(dbSchema.webhookQueue);

			// Count webhooks by status
			const stats: {
				pending: number;
				processing: number;
				completed: number;
				failed: number;
				total: number;
				isProcessing: boolean;
				activeWebhook?: {
					id: string;
					source: string;
					sourceId: string;
					startedAt?: Date;
				};
			} = {
				pending: 0,
				processing: 0,
				completed: 0,
				failed: 0,
				total: statusCounts.length,
				isProcessing: false,
			};

			// Get processing state
			const queueState = await Process.isQueueProcessing();
			stats.isProcessing = queueState.isProcessing;

			if (
				queueState.isProcessing &&
				queueState.activeWebhookId &&
				queueState.activeWebhookSource &&
				queueState.activeWebhookSourceId
			) {
				stats.activeWebhook = {
					id: queueState.activeWebhookId,
					source: queueState.activeWebhookSource,
					sourceId: queueState.activeWebhookSourceId,
					startedAt: queueState.processingStartedAt,
				};
			}

			// Count by status
			for (const row of statusCounts) {
				switch (row.status) {
					case "pending":
						stats.pending++;
						break;
					case "processing":
						stats.processing++;
						break;
					case "completed":
						stats.completed++;
						break;
					case "failed":
						stats.failed++;
						break;
				}
			}

			return stats;
		} catch (error) {
			logError("Failed to get queue statistics", error);
			return {
				pending: 0,
				processing: 0,
				completed: 0,
				failed: 0,
				total: 0,
				isProcessing: false,
			};
		}
	}

	async reteriveContext(id: string | number, type: "patient" | "appointment" = "patient",
	) {
		const res = await this.db.query.webhookQueue.findFirst({
			where: and(
				eq(dbSchema.webhookQueue.sourceId, String(id)),
				eq(dbSchema.webhookQueue.type, type),
			),
		});
		if (res) {
			this.webhookRecord = res;
			return this;
		} else {
			throw new Error("Webhook not found");
		}
	}
}

/**
 * Get Process instance from Hono context
 *
 * Retrieves or creates a Process instance attached to the current Hono context.
 * This allows sharing Process instances across request handlers.
 *
 * @returns Process instance from context
 */
export const useProcess = (): Process => {
	try {
		const c = getContext<Env>();

		// Try to get existing process from context variables
		const variables = c.var as Record<string, unknown>;
		let process = variables.process as Process | undefined;

		if (!process) {
			process = new Process();
			// Store in context variables
			variables.process = process;
		}

		return process;
	} catch {
		// Fallback if context is not available
		return new Process();
	}
};

/**
 * Hono middleware to attach Process to context
 *
 * Middleware that automatically creates and attaches a Process instance
 * to the Hono context for use in request handlers.
 *
 * @example
 * ```typescript
 * app.use("*", processMiddleware());
 *
 * app.post("/webhook", async (c) => {
 *   const process = useProcess();
 *   await process.add(payload);
 *   // ... handle webhook
 * });
 * ```
 */
export const processMiddleware = () => {
	return async (
		c: { var: Record<string, unknown> },
		next: () => Promise<void>,
	) => {
		c.var.process = new Process();
		await next();
	};
};
